#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Word文档处理工具
主要功能：
1. 修改文档中的图片说明文字
2. 调整图片编号顺序
3. 合并图2和图3的说明
"""

import os
import sys
from pathlib import Path
from docx import Document
import re

class SimpleDocxProcessor:
    def __init__(self, docx_path):
        """
        初始化文档处理器
        
        Args:
            docx_path: Word文档路径
        """
        self.docx_path = Path(docx_path)
    
    def update_text_content(self, doc):
        """
        更新文档中的文字内容
        """
        print("正在更新文档文字内容...")
        
        changes_made = 0
        
        # 遍历所有段落
        for paragraph in doc.paragraphs:
            text = paragraph.text
            original_text = text
            
            # 处理图2和图3的合并
            if "图2" in text or "图3" in text:
                # 查找包含图2和图3说明的段落
                if "DNB1101BB" in text and "芯片" in text:
                    # 这可能是图2的说明
                    new_text = re.sub(
                        r'图2[：:][^。]*?。',
                        '图2：DNB1101BB芯片连接电路图及外部电流源电路详细图。',
                        text
                    )
                    if new_text != text:
                        paragraph.text = new_text
                        print(f"已更新图2说明")
                        changes_made += 1
                        continue
                
                elif "外部电流源" in text:
                    # 这可能是图3的说明，需要删除或合并到图2
                    print(f"找到图3说明段落，将其删除：{text[:50]}...")
                    paragraph.text = ""  # 清空图3的说明
                    changes_made += 1
                    continue
            
            # 更新其他图片编号（图4-10变为图3-9）
            for old_num in range(10, 3, -1):  # 从10到4倒序处理
                new_num = old_num - 1  # 由于删除了图3，所以编号减1
                pattern = f'图{old_num}'
                if pattern in text:
                    text = text.replace(pattern, f'图{new_num}')
            
            if text != original_text:
                paragraph.text = text
                print(f"已更新段落中的图片编号")
                changes_made += 1
        
        # 遍历表格中的文字
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        text = paragraph.text
                        original_text = text
                        
                        # 更新图片编号
                        for old_num in range(10, 3, -1):
                            new_num = old_num - 1
                            pattern = f'图{old_num}'
                            if pattern in text:
                                text = text.replace(pattern, f'图{new_num}')
                        
                        if text != original_text:
                            paragraph.text = text
                            changes_made += 1
        
        print(f"总共进行了 {changes_made} 处修改")
        return changes_made > 0
    
    def process(self, output_path=None):
        """
        执行文档处理
        """
        try:
            if output_path is None:
                output_path = self.docx_path.parent / f"{self.docx_path.stem}_文字修改版.docx"
            
            print("开始处理Word文档...")
            print(f"输入文档：{self.docx_path}")
            print(f"输出文档：{output_path}")
            
            # 打开文档
            doc = Document(self.docx_path)
            
            # 更新文字内容
            success = self.update_text_content(doc)
            
            if success:
                # 保存文档
                doc.save(output_path)
                print(f"文档已保存到：{output_path}")
                print("✅ 文档文字处理完成！")
                return True
            else:
                print("⚠️ 未发现需要修改的内容")
                return False
            
        except Exception as e:
            print(f"处理文档时出错：{e}")
            return False

def main():
    """
    主函数
    """
    # 设置文件路径
    docx_path = "DNB1101BB发明专利申请完整文档_含全部附图.docx"
    
    # 检查文件是否存在
    if not Path(docx_path).exists():
        print(f"错误：找不到文档文件 {docx_path}")
        return
    
    # 创建处理器并执行
    processor = SimpleDocxProcessor(docx_path)
    success = processor.process()
    
    if success:
        print("\n📝 文档文字修改完成！")
        print("主要变更：")
        print("1. 图2和图3的说明已合并")
        print("2. 图片编号已重新调整（图4-10变为图3-9）")
        print("\n📋 接下来需要手动操作：")
        print("1. 打开修改后的文档")
        print("2. 手动替换文档中的图片")
        print("3. 删除多余的图3，保留合并后的图2")
    else:
        print("\n❌ 文档处理失败或无需修改")

if __name__ == "__main__":
    main()

# Word文档图片替换和合并操作指导

## 📋 任务概述
将Word文档中的图片替换为新版本，并将图2和图3合并为一个图，同时调整图片编号顺序。

## ✅ 已完成的工作

### 1. 文档文字修改
- ✅ 已运行 `simple_docx_processor.py`
- ✅ 已生成 `DNB1101BB发明专利申请完整文档_含全部附图_文字修改版.docx`
- ✅ 图片编号已调整（图4-10变为图3-9）
- ✅ 进行了40处文字修改

### 2. 图片合并准备
- ✅ 已创建 `合并指导图.png` - 详细的操作步骤说明
- ✅ 已创建 `合并参考` 文件夹，包含图2和图3的副本
- ✅ 已创建 `SVG转换指导.md` - SVG文件转换方法

## 🔧 接下来需要手动完成的步骤

### 步骤1：转换SVG文件为PNG
由于SVG文件在Word中的兼容性问题，建议先转换为PNG格式：

**推荐方法：使用在线转换工具**
1. 访问 https://convertio.co/zh/svg-png/
2. 上传需要的SVG文件
3. 下载转换后的PNG文件

**需要转换的文件：**
- 图1_系统整体架构图_优化版.svg
- 图2_DNB1101BB芯片连接电路图.svg  
- 图3_外部电流源电路详细图.svg
- 图4_EIS测试完整流程图.svg
- 图5_典型电池奈奎斯特曲线示例图_修正版.svg
- 图6_多维参数提取算法流程图.svg
- 图7_九档智能分组决策流程图_简化版.svg
- 图8_Modbus RTU通信协议时序图_标准版.svg
- 图9_频率扫描序列设置图.svg
- 图10_增益自适应调节流程图_最终修正版.svg

### 步骤2：合并图2和图3
1. 将图2和图3都转换为PNG格式
2. 使用图片编辑软件（推荐Photoshop、GIMP或Paint.NET）：
   - 创建新画布，尺寸足够容纳两张图
   - 将图2放在上方
   - 将图3放在下方  
   - 两图之间留适当间距
   - 保存为PNG格式，命名为 `图2_合并图.png`

### 步骤3：替换Word文档中的图片
1. 打开 `DNB1101BB发明专利申请完整文档_含全部附图_文字修改版.docx`
2. 逐一替换文档中的图片：
   - 右键点击图片 → 更改图片 → 从文件
   - 选择对应的新PNG图片

**图片对应关系：**
- 图1 → 图1_系统整体架构图_优化版.png
- 图2 → 图2_合并图.png（合并后的图片）
- 图3 → 图4_EIS测试完整流程图.png
- 图4 → 图5_典型电池奈奎斯特曲线示例图_修正版.png
- 图5 → 图6_多维参数提取算法流程图.png
- 图6 → 图7_九档智能分组决策流程图_简化版.png
- 图7 → 图8_Modbus RTU通信协议时序图_标准版.png
- 图8 → 图9_频率扫描序列设置图.png
- 图9 → 图10_增益自适应调节流程图_最终修正版.png

### 步骤4：最终检查
1. 检查所有图片是否正确替换
2. 检查图片编号是否正确（应该是1-9，共9张图）
3. 检查图2的说明是否为："图2：DNB1101BB芯片连接电路图及外部电流源电路详细图"
4. 确认没有多余的图3引用

## 📁 文件说明

### 生成的文件
- `DNB1101BB发明专利申请完整文档_含全部附图_文字修改版.docx` - 文字已修改的文档
- `合并指导图.png` - 可视化操作指导
- `SVG转换指导.md` - SVG转换方法详解
- `合并参考/` - 包含图2和图3的副本

### 工具脚本
- `simple_docx_processor.py` - 文档文字处理脚本
- `create_combined_image.py` - 图片合并准备脚本
- `replace_images_in_docx.py` - 完整处理脚本（需要Cairo库支持）

## ⚠️ 注意事项
1. 建议在操作前备份原始文档
2. SVG转PNG时建议使用300 DPI以保证清晰度
3. 合并图片时注意保持原图的比例和清晰度
4. 替换图片后检查文档的整体布局

## 🎯 预期结果
完成后的文档应该：
- 包含9张图片（原来10张，图2和图3合并为1张）
- 所有图片都是高质量的PNG格式
- 图片编号连续（图1到图9）
- 图2的说明包含了原图2和图3的内容
- 文档布局保持整洁美观

## 📞 如需帮助
如果在操作过程中遇到问题，可以：
1. 查看 `合并指导图.png` 获取可视化指导
2. 参考 `SVG转换指导.md` 了解转换方法
3. 重新运行相关Python脚本进行调整

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档图片替换和合并工具
功能：
1. 替换Word文档中的所有图片
2. 将图2和图3合并为一个图
3. 修改相关的图片说明文字
4. 调整图片编号顺序
"""

import os
import sys
from pathlib import Path
import zipfile
import shutil
import xml.etree.ElementTree as ET
from PIL import Image, ImageDraw, ImageFont
import tempfile
import re
from docx import Document
from docx.shared import Inches
try:
    import cairosvg
    CAIROSVG_AVAILABLE = True
except ImportError:
    CAIROSVG_AVAILABLE = False
    print("警告：cairosvg不可用，将跳过SVG转换")

import io

class DocxImageReplacer:
    def __init__(self, docx_path, images_dir):
        """
        初始化文档图片替换器

        Args:
            docx_path: Word文档路径
            images_dir: 新图片所在目录
        """
        self.docx_path = Path(docx_path)
        self.images_dir = Path(images_dir)
        self.temp_dir = None

        # 图片文件映射（按新的编号顺序）
        self.image_files = {
            1: "图1_系统整体架构图_优化版.svg",
            2: None,  # 这将是合并后的图片
            3: "图4_EIS测试完整流程图.svg",
            4: "图5_典型电池奈奎斯特曲线示例图_修正版.svg",
            5: "图6_多维参数提取算法流程图.svg",
            6: "图7_九档智能分组决策流程图_简化版.svg",
            7: "图8_Modbus RTU通信协议时序图_标准版.svg",
            8: "图9_频率扫描序列设置图.svg",
            9: "图10_增益自适应调节流程图_最终修正版.svg"
        }

    def svg_to_png(self, svg_path, output_path=None):
        """
        将SVG文件转换为PNG
        """
        if not CAIROSVG_AVAILABLE:
            print(f"跳过SVG转换：{svg_path}")
            return svg_path  # 返回原始SVG路径

        try:
            if output_path is None:
                output_path = svg_path.with_suffix('.png')

            # 读取SVG文件并转换为PNG
            with open(svg_path, 'rb') as svg_file:
                svg_data = svg_file.read()

            png_data = cairosvg.svg2png(bytestring=svg_data)

            with open(output_path, 'wb') as png_file:
                png_file.write(png_data)

            return output_path
        except Exception as e:
            print(f"转换SVG文件 {svg_path} 时出错：{e}")
            return svg_path  # 返回原始SVG路径

    def create_combined_image(self):
        """
        创建图2和图3的合并图片
        """
        print("正在创建图2和图3的合并图片...")

        # 创建临时目录
        if self.temp_dir is None:
            self.temp_dir = Path(tempfile.mkdtemp())

        # 读取图2和图3
        img2_path = self.images_dir / "图2_DNB1101BB芯片连接电路图.svg"
        img3_path = self.images_dir / "图3_外部电流源电路详细图.svg"

        if not img2_path.exists() or not img3_path.exists():
            print(f"警告：找不到图2或图3文件")
            print(f"图2路径：{img2_path}")
            print(f"图3路径：{img3_path}")
            return None

        try:
            # 转换SVG为PNG
            temp_img2 = self.temp_dir / "temp_img2.png"
            temp_img3 = self.temp_dir / "temp_img3.png"

            self.svg_to_png(img2_path, temp_img2)
            self.svg_to_png(img3_path, temp_img3)

            # 打开图片
            img2 = Image.open(temp_img2)
            img3 = Image.open(temp_img3)

            # 计算合并后的图片尺寸
            max_width = max(img2.width, img3.width)
            total_height = img2.height + img3.height + 50  # 50像素间距

            # 创建新的合并图片
            combined_img = Image.new('RGB', (max_width, total_height), 'white')

            # 粘贴图2（上方）
            x_offset2 = (max_width - img2.width) // 2
            combined_img.paste(img2, (x_offset2, 0))

            # 粘贴图3（下方）
            x_offset3 = (max_width - img3.width) // 2
            combined_img.paste(img3, (x_offset3, img2.height + 50))

            # 保存合并后的图片
            combined_path = self.temp_dir / "图2_合并图.png"
            combined_img.save(combined_path, 'PNG')

            print(f"合并图片已保存到：{combined_path}")
            return combined_path

        except Exception as e:
            print(f"创建合并图片时出错：{e}")
            return None
    
    def process_document_with_python_docx(self, output_path):
        """
        使用python-docx库处理文档
        """
        print("正在使用python-docx处理文档...")

        try:
            # 打开原始文档
            doc = Document(self.docx_path)

            # 创建合并图片
            combined_img_path = self.create_combined_image()
            if not combined_img_path:
                print("无法创建合并图片，跳过图片替换")
                return False

            # 准备所有图片的PNG版本
            png_images = {}

            # 处理合并图片（图2）
            png_images[2] = combined_img_path

            # 处理其他图片
            for fig_num, filename in self.image_files.items():
                if fig_num == 2 or filename is None:
                    continue

                svg_path = self.images_dir / filename
                if svg_path.exists():
                    png_path = self.temp_dir / f"图{fig_num}.png"
                    if self.svg_to_png(svg_path, png_path):
                        png_images[fig_num] = png_path
                    else:
                        print(f"警告：无法转换图{fig_num}")
                else:
                    print(f"警告：找不到文件 {filename}")

            # 更新文档中的文字内容
            self.update_text_content(doc)

            # 注意：python-docx不能直接替换现有图片，需要手动处理
            # 这里我们先保存文档，然后提示用户手动替换图片
            doc.save(output_path)

            print(f"文档已保存到：{output_path}")
            print("\n重要提示：")
            print("由于python-docx库的限制，图片需要手动替换。")
            print("已准备好的PNG图片文件：")
            for fig_num, png_path in png_images.items():
                print(f"  图{fig_num}: {png_path}")

            return True

        except Exception as e:
            print(f"处理文档时出错：{e}")
            return False

    def update_text_content(self, doc):
        """
        更新文档中的文字内容
        """
        print("正在更新文档文字内容...")

        # 遍历所有段落
        for paragraph in doc.paragraphs:
            text = paragraph.text

            # 合并图2和图3的说明
            if "图2" in text and "图3" in text:
                # 替换包含图2和图3的段落
                new_text = re.sub(
                    r'图2[^。]*?。[^图]*图3[^。]*?。',
                    '图2：DNB1101BB芯片连接电路图及外部电流源电路详细图。',
                    text,
                    flags=re.DOTALL
                )
                if new_text != text:
                    paragraph.text = new_text
                    print(f"已更新段落：{new_text[:50]}...")

            # 更新其他图片编号
            original_text = text
            for old_num in range(10, 3, -1):  # 从10到4倒序处理
                new_num = old_num - 1  # 由于删除了一张图，所以编号减1
                text = re.sub(f'图{old_num}', f'图{new_num}', text)

            if text != original_text:
                paragraph.text = text
                print(f"已更新图片编号：{text[:50]}...")

        # 遍历表格中的文字
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        text = paragraph.text
                        original_text = text

                        # 更新图片编号
                        for old_num in range(10, 3, -1):
                            new_num = old_num - 1
                            text = re.sub(f'图{old_num}', f'图{new_num}', text)

                        if text != original_text:
                            paragraph.text = text

    def cleanup(self):
        """
        清理临时文件
        """
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            print("临时文件已清理")
    
    def process(self, output_path=None):
        """
        执行完整的处理流程
        """
        try:
            if output_path is None:
                output_path = self.docx_path.parent / f"{self.docx_path.stem}_修改版.docx"

            print("开始处理Word文档...")
            print(f"输入文档：{self.docx_path}")
            print(f"图片目录：{self.images_dir}")
            print(f"输出文档：{output_path}")

            # 创建临时目录
            self.temp_dir = Path(tempfile.mkdtemp())

            # 使用python-docx处理文档
            success = self.process_document_with_python_docx(output_path)

            if success:
                print("文档处理完成！")
            else:
                print("文档处理失败")

            return success

        except Exception as e:
            print(f"处理过程中出现错误：{e}")
            return False
        finally:
            self.cleanup()

def main():
    """
    主函数
    """
    # 设置文件路径
    docx_path = "DNB1101BB发明专利申请完整文档_含全部附图.docx"
    images_dir = "."  # 当前目录
    
    # 检查文件是否存在
    if not Path(docx_path).exists():
        print(f"错误：找不到文档文件 {docx_path}")
        return
    
    if not Path(images_dir).exists():
        print(f"错误：找不到图片目录 {images_dir}")
        return
    
    # 创建处理器并执行
    replacer = DocxImageReplacer(docx_path, images_dir)
    success = replacer.process()
    
    if success:
        print("\n✅ 文档处理成功完成！")
        print("主要变更：")
        print("1. 所有图片已替换为新版本")
        print("2. 图2和图3已合并为一个图")
        print("3. 图片编号已重新调整（共9张图）")
        print("4. 相关说明文字已更新")
    else:
        print("\n❌ 文档处理失败，请检查错误信息")

if __name__ == "__main__":
    main()

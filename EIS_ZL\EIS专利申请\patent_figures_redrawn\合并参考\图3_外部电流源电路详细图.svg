<?xml version="1.0" encoding="UTF-8"?>
<svg width="1100" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: "Microsoft YaHei", <PERSON>l; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .component-label { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; }
      .value-label { font-family: "Microsoft YaHei", Arial; font-size: 8px; text-anchor: middle; fill: #666; }
      .pin-label { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; }
      .connection { stroke: #333; stroke-width: 1.5; fill: none; }
      .power-line { stroke: #d32f2f; stroke-width: 2; fill: none; }
      .ground-line { stroke: #388e3c; stroke-width: 2; fill: none; }
      .signal-line { stroke: #1976d2; stroke-width: 1.5; fill: none; }
      .current-line { stroke: #ff6f00; stroke-width: 2; fill: none; }
    </style>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="550" y="30" class="title">图3 外部电流源电路详细图</text>
  
  <!-- 控制信号输入区域 -->
  <rect x="50" y="80" width="200" height="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="150" y="105" class="section-title">控制信号输入</text>
  
  <!-- DNB1101BB控制信号 -->
  <rect x="70" y="120" width="80" height="25" fill="#fff" stroke="#333" rx="2"/>
  <text x="110" y="138" class="component-label">VSW控制</text>
  
  <rect x="70" y="155" width="80" height="25" fill="#fff" stroke="#333" rx="2"/>
  <text x="110" y="173" class="component-label">VDR控制</text>
  
  <rect x="160" y="120" width="80" height="25" fill="#fff" stroke="#333" rx="2"/>
  <text x="200" y="138" class="component-label">电流设定</text>
  
  <rect x="160" y="155" width="80" height="25" fill="#fff" stroke="#333" rx="2"/>
  <text x="200" y="173" class="component-label">使能信号</text>
  
  <!-- MOSFET驱动电路区域 -->
  <rect x="300" y="80" width="250" height="200" fill="#fff3e0" stroke="#ff6f00" stroke-width="2" rx="5"/>
  <text x="425" y="105" class="section-title">MOSFET驱动电路</text>
  
  <!-- 驱动芯片 -->
  <rect x="320" y="130" width="100" height="60" fill="#fff" stroke="#333" stroke-width="1" rx="3"/>
  <text x="370" y="150" class="component-label">IR2110</text>
  <text x="370" y="165" class="component-label">驱动芯片</text>
  
  <!-- 驱动芯片引脚 -->
  <text x="310" y="145" class="pin-label">HIN</text>
  <text x="310" y="160" class="pin-label">LIN</text>
  <text x="310" y="175" class="pin-label">VCC</text>
  
  <text x="430" y="145" class="pin-label">HO</text>
  <text x="430" y="160" class="pin-label">LO</text>
  <text x="430" y="175" class="pin-label">VS</text>
  
  <!-- 自举电路 -->
  <rect x="450" y="130" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="490" y="150" class="component-label">自举电路</text>
  
  <!-- 电容和二极管 -->
  <circle cx="470" cy="170" r="8" fill="#fff" stroke="#333"/>
  <text x="470" y="175" class="value-label">C</text>
  <text x="470" y="190" class="value-label">100nF</text>
  
  <polygon points="500,165 510,170 500,175" fill="#333"/>
  <text x="505" y="190" class="value-label">D1</text>
  
  <!-- 功率MOSFET区域 -->
  <rect x="600" y="80" width="200" height="300" fill="#ffebee" stroke="#d32f2f" stroke-width="2" rx="5"/>
  <text x="700" y="105" class="section-title">功率MOSFET</text>
  
  <!-- 上管MOSFET -->
  <rect x="650" y="130" width="100" height="50" fill="#fff" stroke="#333" rx="3"/>
  <text x="700" y="150" class="component-label">Q1 (上管)</text>
  <text x="700" y="165" class="component-label">IRF540N</text>
  
  <!-- 下管MOSFET -->
  <rect x="650" y="200" width="100" height="50" fill="#fff" stroke="#333" rx="3"/>
  <text x="700" y="220" class="component-label">Q2 (下管)</text>
  <text x="700" y="235" class="component-label">IRF540N</text>
  
  <!-- 电流检测电阻 -->
  <rect x="650" y="270" width="100" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="700" y="290" class="component-label">R_sense</text>
  <text x="700" y="305" class="value-label">0.1Ω 5W</text>
  
  <!-- 保护电路区域 -->
  <rect x="850" y="80" width="200" height="300" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="950" y="105" class="section-title">保护电路</text>
  
  <!-- 过流保护 -->
  <rect x="870" y="130" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="910" y="150" class="component-label">过流保护</text>
  
  <!-- 过压保护 -->
  <rect x="870" y="170" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="910" y="190" class="component-label">过压保护</text>
  
  <!-- 温度保护 -->
  <rect x="870" y="210" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="910" y="230" class="component-label">温度保护</text>
  
  <!-- 反向保护二极管 -->
  <rect x="870" y="250" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="910" y="270" class="component-label">反向保护</text>
  
  <!-- 电流控制环路区域 -->
  <rect x="50" y="450" width="500" height="200" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" rx="5"/>
  <text x="300" y="475" class="section-title">电流控制环路</text>
  
  <!-- 电流设定 -->
  <rect x="70" y="500" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="110" y="520" class="component-label">电流设定</text>
  
  <!-- 比较器 -->
  <polygon points="180,500 220,515 180,530" fill="#fff" stroke="#333"/>
  <text x="200" y="520" class="component-label">比较器</text>
  
  <!-- PI控制器 -->
  <rect x="250" y="500" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="290" y="520" class="component-label">PI控制器</text>
  
  <!-- PWM发生器 -->
  <rect x="360" y="500" width="80" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="400" y="520" class="component-label">PWM发生器</text>
  
  <!-- 电流反馈 -->
  <rect x="250" y="580" width="100" height="30" fill="#fff" stroke="#333" rx="2"/>
  <text x="300" y="600" class="component-label">电流反馈放大器</text>
  
  <!-- 输出端子区域 -->
  <rect x="600" y="450" width="200" height="150" fill="#fffde7" stroke="#f57f17" stroke-width="2" rx="5"/>
  <text x="700" y="475" class="section-title">输出端子</text>
  
  <!-- 正输出端子 -->
  <circle cx="650" cy="520" r="15" fill="#d32f2f" stroke="#333" stroke-width="2"/>
  <text x="650" y="525" class="component-label" fill="#fff">+</text>
  <text x="680" y="525" class="component-label">正极输出</text>
  
  <!-- 负输出端子 -->
  <circle cx="650" cy="560" r="15" fill="#388e3c" stroke="#333" stroke-width="2"/>
  <text x="650" y="565" class="component-label" fill="#fff">-</text>
  <text x="680" y="565" class="component-label">负极输出</text>
  
  <!-- 连接线 -->
  <!-- 控制信号到驱动电路 -->
  <line x1="150" y1="132" x2="320" y2="145" class="signal-line"/>
  <line x1="150" y1="167" x2="320" y2="160" class="signal-line"/>
  
  <!-- 驱动电路到MOSFET -->
  <line x1="420" y1="145" x2="650" y2="155" class="signal-line"/>
  <line x1="420" y1="160" x2="650" y2="225" class="signal-line"/>
  
  <!-- 自举电路连接 -->
  <line x1="450" y1="145" x2="430" y2="175" class="connection"/>
  
  <!-- MOSFET到输出 -->
  <line x1="750" y1="155" x2="650" y2="520" class="current-line"/>
  <line x1="750" y1="285" x2="650" y2="560" class="current-line"/>
  
  <!-- 电流检测反馈 -->
  <line x1="700" y1="300" x2="300" y2="580" class="signal-line"/>
  
  <!-- 控制环路连接 -->
  <line x1="150" y1="515" x2="180" y2="515" class="signal-line"/>
  <line x1="220" y1="515" x2="250" y2="515" class="signal-line"/>
  <line x1="330" y1="515" x2="360" y2="515" class="signal-line"/>
  <line x1="440" y1="515" x2="650" y2="155" class="signal-line"/>
  
  <!-- 反馈环路 -->
  <line x1="300" y1="580" x2="200" y2="530" class="signal-line"/>
  
  <!-- 保护电路连接 -->
  <line x1="800" y1="155" x2="870" y2="145" class="signal-line"/>
  <line x1="800" y1="225" x2="870" y2="185" class="signal-line"/>
  
  <!-- 技术参数标注 -->
  <text x="50" y="720" class="component-label">技术参数：</text>
  <text x="50" y="740" class="value-label">• 输出电流范围: 1mA - 1A</text>
  <text x="50" y="755" class="value-label">• 电流精度: ±0.1%</text>
  <text x="50" y="770" class="value-label">• 响应时间: &lt;1ms</text>
  
  <text x="300" y="740" class="value-label">• 输出电压范围: 0-12V</text>
  <text x="300" y="755" class="value-label">• 保护功能: 过流/过压/过温</text>
  <text x="300" y="770" class="value-label">• PWM频率: 100kHz</text>
</svg>

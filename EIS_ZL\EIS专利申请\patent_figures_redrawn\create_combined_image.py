#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片合并工具
功能：创建图2和图3的合并图片
注意：由于SVG处理的复杂性，这个脚本提供指导和准备工作
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import shutil

class ImageCombiner:
    def __init__(self, images_dir):
        """
        初始化图片合并器
        
        Args:
            images_dir: 图片所在目录
        """
        self.images_dir = Path(images_dir)
    
    def create_instruction_image(self):
        """
        创建一个指导图片，说明如何合并图2和图3
        """
        print("正在创建合并指导图片...")
        
        # 创建一个指导图片
        width, height = 800, 600
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        
        try:
            # 尝试使用系统字体
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_medium = ImageFont.truetype("arial.ttf", 18)
            font_small = ImageFont.truetype("arial.ttf", 14)
        except:
            # 如果找不到字体，使用默认字体
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # 绘制标题
        title = "图2和图3合并指导"
        draw.text((50, 50), title, fill='black', font=font_large)
        
        # 绘制说明文字
        instructions = [
            "请按照以下步骤手动合并图2和图3：",
            "",
            "1. 打开图2_DNB1101BB芯片连接电路图.svg",
            "2. 打开图3_外部电流源电路详细图.svg", 
            "3. 使用图片编辑软件（如Photoshop、GIMP等）：",
            "   - 将图2放在上方",
            "   - 将图3放在下方",
            "   - 两图之间留适当间距",
            "   - 保存为PNG格式，命名为'图2_合并图.png'",
            "",
            "4. 在Word文档中：",
            "   - 删除原来的图3",
            "   - 将图2替换为合并后的图片",
            "   - 更新图2的说明为：",
            "     '图2：DNB1101BB芯片连接电路图及外部电流源电路详细图'",
            "",
            "5. 更新后续图片编号：",
            "   - 原图4 → 图3",
            "   - 原图5 → 图4", 
            "   - 原图6 → 图5",
            "   - 原图7 → 图6",
            "   - 原图8 → 图7",
            "   - 原图9 → 图8",
            "   - 原图10 → 图9"
        ]
        
        y_pos = 100
        for line in instructions:
            draw.text((50, y_pos), line, fill='black', font=font_small)
            y_pos += 20
        
        # 保存指导图片
        output_path = self.images_dir / "合并指导图.png"
        img.save(output_path)
        print(f"指导图片已保存到：{output_path}")
        
        return output_path
    
    def list_available_images(self):
        """
        列出可用的图片文件
        """
        print("\n📁 当前目录中的图片文件：")
        
        svg_files = list(self.images_dir.glob("*.svg"))
        png_files = list(self.images_dir.glob("*.png"))
        
        print("SVG文件：")
        for svg_file in sorted(svg_files):
            print(f"  📄 {svg_file.name}")
        
        print("\nPNG文件：")
        for png_file in sorted(png_files):
            print(f"  🖼️ {png_file.name}")
        
        return svg_files, png_files
    
    def copy_images_for_reference(self):
        """
        复制需要合并的图片到一个单独的文件夹以便参考
        """
        print("正在准备参考文件...")
        
        # 创建参考文件夹
        ref_dir = self.images_dir / "合并参考"
        ref_dir.mkdir(exist_ok=True)
        
        # 复制图2和图3
        img2_path = self.images_dir / "图2_DNB1101BB芯片连接电路图.svg"
        img3_path = self.images_dir / "图3_外部电流源电路详细图.svg"
        
        files_copied = []
        
        if img2_path.exists():
            shutil.copy2(img2_path, ref_dir / "图2_DNB1101BB芯片连接电路图.svg")
            files_copied.append("图2")
            print(f"✅ 已复制图2到参考文件夹")
        else:
            print(f"⚠️ 找不到图2文件：{img2_path}")
        
        if img3_path.exists():
            shutil.copy2(img3_path, ref_dir / "图3_外部电流源电路详细图.svg")
            files_copied.append("图3")
            print(f"✅ 已复制图3到参考文件夹")
        else:
            print(f"⚠️ 找不到图3文件：{img3_path}")
        
        if files_copied:
            print(f"\n📂 参考文件已保存到：{ref_dir}")
            return ref_dir
        else:
            print("❌ 未找到需要合并的图片文件")
            return None
    
    def create_svg_conversion_guide(self):
        """
        创建SVG转换指导文件
        """
        guide_content = """# SVG文件转换指导

## 方法1：使用在线转换工具
1. 访问 https://convertio.co/zh/svg-png/ 或类似网站
2. 上传SVG文件
3. 选择PNG格式
4. 下载转换后的文件

## 方法2：使用Inkscape（免费软件）
1. 下载并安装Inkscape：https://inkscape.org/
2. 打开SVG文件
3. 文件 → 导出 → 导出为PNG
4. 设置合适的分辨率（建议300 DPI）
5. 导出

## 方法3：使用Adobe Illustrator
1. 打开SVG文件
2. 文件 → 导出 → 导出为...
3. 选择PNG格式
4. 设置分辨率和质量
5. 导出

## 方法4：使用浏览器
1. 在浏览器中打开SVG文件
2. 右键 → 检查元素
3. 在控制台中运行截图命令
4. 或使用浏览器的打印功能保存为PDF，再转换为PNG

## 合并步骤
1. 将图2和图3都转换为PNG格式
2. 使用图片编辑软件（Photoshop、GIMP、Paint.NET等）
3. 创建新画布，尺寸足够容纳两张图
4. 将图2放在上方，图3放在下方
5. 调整间距和对齐
6. 保存为PNG格式
"""
        
        guide_path = self.images_dir / "SVG转换指导.md"
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"📋 SVG转换指导已保存到：{guide_path}")
        return guide_path

def main():
    """
    主函数
    """
    # 设置图片目录
    images_dir = "."  # 当前目录
    
    print("🖼️ 图片合并工具")
    print("=" * 50)
    
    # 创建合并器
    combiner = ImageCombiner(images_dir)
    
    # 列出可用图片
    svg_files, png_files = combiner.list_available_images()
    
    # 创建指导图片
    combiner.create_instruction_image()
    
    # 复制参考文件
    ref_dir = combiner.copy_images_for_reference()
    
    # 创建转换指导
    combiner.create_svg_conversion_guide()
    
    print("\n" + "=" * 50)
    print("📋 操作总结：")
    print("1. ✅ 已创建合并指导图")
    print("2. ✅ 已复制参考文件到'合并参考'文件夹")
    print("3. ✅ 已创建SVG转换指导文档")
    print("\n🔧 接下来的步骤：")
    print("1. 查看'合并指导图.png'了解详细步骤")
    print("2. 参考'SVG转换指导.md'转换SVG文件")
    print("3. 使用图片编辑软件合并图2和图3")
    print("4. 运行simple_docx_processor.py修改文档文字")

if __name__ == "__main__":
    main()
